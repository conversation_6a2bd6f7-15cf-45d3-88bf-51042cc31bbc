import React, { useState } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useProjectStore } from '@/stores/projectStore';
import { useTeamStore } from '@/stores/teamStore';

interface AddScreenModalProps {
  isOpen: boolean;
  onClose: () => void;
  projectId: string;
}

export function AddScreenModal({ isOpen, onClose, projectId }: AddScreenModalProps) {
  const [title, setTitle] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);
  
  const { addScreen } = useProjectStore();
  const { employees } = useTeamStore();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim()) return;

    addScreen(projectId, {
      title,
      imageUrl,
      assignedTo: selectedMembers,
      comments: [],
      tasks: [],
      order: Date.now(), // Simple ordering by creation time
    });

    // Reset form
    setTitle('');
    setImageUrl('');
    setSelectedMembers([]);
    onClose();
  };

  const toggleMember = (memberId: string) => {
    setSelectedMembers(prev =>
      prev.includes(memberId)
        ? prev.filter(id => id !== memberId)
        : [...prev, memberId]
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Add New Screen</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              Screen Title
            </label>
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter screen title"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              Screen Image URL
            </label>
            <Input
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
              placeholder="https://example.com/screen-image.jpg"
              type="url"
            />
            <p className="text-xs text-gray-500 mt-1">
              Add a URL to an image that represents this screen design
            </p>
          </div>

          {/* Image Preview */}
          {imageUrl && (
            <div className="space-y-2">
              <label className="block text-sm font-medium">Preview</label>
              <div className="w-full h-32 bg-gray-100 rounded-lg overflow-hidden">
                <img
                  src={imageUrl}
                  alt="Screen preview"
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                    e.currentTarget.parentElement!.innerHTML = `
                      <div class="w-full h-full flex items-center justify-center bg-red-50 text-red-500">
                        <span class="text-sm">Invalid image URL</span>
                      </div>
                    `;
                  }}
                />
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium mb-2">
              Assign Team Members
            </label>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {employees.map((employee) => (
                <label
                  key={employee.id}
                  className="flex items-center space-x-2 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={selectedMembers.includes(employee.id)}
                    onChange={() => toggleMember(employee.id)}
                    className="rounded"
                  />
                  <span className="text-sm">
                    {employee.name} - {employee.role}
                  </span>
                </label>
              ))}
            </div>
            {employees.length === 0 && (
              <p className="text-sm text-gray-500">No team members available</p>
            )}
          </div>

          <div className="flex gap-2 pt-4">
            <Button type="submit" className="flex-1">
              Add Screen
            </Button>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
