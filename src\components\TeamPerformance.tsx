
import React from 'react';
import { TrendingUp, Award, Target } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

interface TeamPerformanceProps {
  detailed?: boolean;
}

export function TeamPerformance({ detailed = false }: TeamPerformanceProps) {
  const performanceData = [
    {
      id: '1',
      name: '<PERSON>',
      role: 'Frontend Developer',
      rating: 4.8,
      completedTasks: 24,
      onTimeDelivery: 95,
      feedback: 'Excellent technical skills and great team collaboration.',
    },
    {
      id: '2',
      name: '<PERSON>',
      role: 'UI/UX Designer',
      rating: 4.6,
      completedTasks: 18,
      onTimeDelivery: 92,
      feedback: 'Creative designs and good attention to detail.',
    },
    {
      id: '3',
      name: '<PERSON>',
      role: 'Backend Developer',
      rating: 4.4,
      completedTasks: 21,
      onTimeDelivery: 89,
      feedback: 'Solid technical execution, could improve communication.',
    },
  ];

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return 'bg-green-100 text-green-800';
    if (rating >= 4.0) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Team Performance
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {performanceData.map((member) => (
            <div key={member.id} className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-start gap-3">
                <Avatar className="h-12 w-12">
                  <AvatarFallback className="bg-gradient-to-br from-pink-500 to-purple-600 text-white">
                    {getInitials(member.name)}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className="font-medium text-gray-900">{member.name}</h4>
                      <p className="text-sm text-gray-500">{member.role}</p>
                    </div>
                    <Badge className={getRatingColor(member.rating)}>
                      ⭐ {member.rating}
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 mb-2">
                    <div className="flex items-center gap-2">
                      <Target className="h-4 w-4 text-blue-500" />
                      <span className="text-sm">Tasks: {member.completedTasks}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Award className="h-4 w-4 text-green-500" />
                      <span className="text-sm">On-time: {member.onTimeDelivery}%</span>
                    </div>
                  </div>
                  
                  {detailed && (
                    <p className="text-sm text-gray-600">{member.feedback}</p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
