
import React from 'react';
import { CheckCircle, Calendar, User } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

export function CompletedTasksOverview() {
  const completedTasks = [
    {
      id: '1',
      title: 'Design Login Screen',
      project: 'SaaS Dashboard',
      assignee: '<PERSON>',
      completedDate: '2024-01-15',
      priority: 'High',
    },
    {
      id: '2',
      title: 'User Research Analysis',
      project: 'Mobile App',
      assignee: '<PERSON>',
      completedDate: '2024-01-14',
      priority: 'Medium',
    },
    {
      id: '3',
      title: 'API Integration',
      project: 'E-commerce Site',
      assignee: '<PERSON>',
      completedDate: '2024-01-13',
      priority: 'High',
    },
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High':
        return 'bg-red-100 text-red-800';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'Low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckCircle className="h-5 w-5" />
          Recently Completed Tasks
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {completedTasks.map((task) => (
            <div key={task.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">{task.title}</h4>
                  <p className="text-sm text-gray-500">{task.project}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-gradient-to-br from-pink-500 to-purple-600 text-white text-xs">
                      {getInitials(task.assignee)}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm text-gray-600">{task.assignee}</span>
                </div>
                
                <Badge className={getPriorityColor(task.priority)}>
                  {task.priority}
                </Badge>
                
                <div className="flex items-center gap-1 text-sm text-gray-500">
                  <Calendar className="h-4 w-4" />
                  {task.completedDate}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
