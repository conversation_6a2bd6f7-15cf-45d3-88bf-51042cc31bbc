
import React, { useState } from 'react';
import { Bell, Mail, MessageSquare, Calendar } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

export function NotificationSettings() {
  const [notifications, setNotifications] = useState({
    email: {
      projectUpdates: true,
      taskAssignments: true,
      deadlineReminders: true,
      teamMessages: false,
    },
    push: {
      projectUpdates: true,
      taskAssignments: true,
      deadlineReminders: true,
      teamMessages: true,
    },
    inApp: {
      projectUpdates: true,
      taskAssignments: true,
      deadlineReminders: true,
      teamMessages: true,
    },
  });

  const updateNotification = (type: string, setting: string, value: boolean) => {
    setNotifications(prev => ({
      ...prev,
      [type]: {
        ...prev[type as keyof typeof prev],
        [setting]: value,
      },
    }));
  };

  const notificationTypes = [
    { key: 'projectUpdates', label: 'Project Updates', icon: Calendar },
    { key: 'taskAssignments', label: 'Task Assignments', icon: MessageSquare },
    { key: 'deadlineReminders', label: 'Deadline Reminders', icon: Bell },
    { key: 'teamMessages', label: 'Team Messages', icon: Mail },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Notification Preferences
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="grid grid-cols-4 gap-4 text-sm font-medium text-gray-700">
            <div></div>
            <div className="text-center">Email</div>
            <div className="text-center">Push</div>
            <div className="text-center">In-App</div>
          </div>
          
          {notificationTypes.map((type) => (
            <div key={type.key} className="grid grid-cols-4 gap-4 items-center py-3 border-b border-gray-100">
              <div className="flex items-center gap-2">
                <type.icon className="h-4 w-4 text-gray-500" />
                <Label>{type.label}</Label>
              </div>
              
              <div className="flex justify-center">
                <Switch
                  checked={notifications.email[type.key as keyof typeof notifications.email]}
                  onCheckedChange={(checked) => updateNotification('email', type.key, checked)}
                />
              </div>
              
              <div className="flex justify-center">
                <Switch
                  checked={notifications.push[type.key as keyof typeof notifications.push]}
                  onCheckedChange={(checked) => updateNotification('push', type.key, checked)}
                />
              </div>
              
              <div className="flex justify-center">
                <Switch
                  checked={notifications.inApp[type.key as keyof typeof notifications.inApp]}
                  onCheckedChange={(checked) => updateNotification('inApp', type.key, checked)}
                />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
