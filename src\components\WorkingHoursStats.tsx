
import React from 'react';
import { TrendingUp, Users, Clock, Calendar } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useTeamStore } from '@/stores/teamStore';

export function WorkingHoursStats() {
  const { employees, timeEntries, attendanceRecords } = useTeamStore();

  const getWeeklyHours = (employeeId: string) => {
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    
    return timeEntries
      .filter(entry => 
        entry.employeeId === employeeId && 
        entry.date >= weekStart
      )
      .reduce((total, entry) => total + entry.duration, 0) / 60; // Convert to hours
  };

  const getAttendanceRate = (employeeId: string) => {
    const last30Days = attendanceRecords.filter(record => 
      record.employeeId === employeeId &&
      record.date >= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    );
    
    const presentDays = last30Days.filter(record => 
      record.status === 'Present' || record.status === 'Late'
    ).length;
    
    return last30Days.length > 0 ? (presentDays / last30Days.length) * 100 : 0;
  };

  const getStatusColor = (hours: number) => {
    if (hours >= 40) return 'bg-green-100 text-green-800';
    if (hours >= 30) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Working Hours Statistics
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Employee</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>This Week</TableHead>
              <TableHead>Attendance Rate</TableHead>
              <TableHead>Working Hours</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {employees.map((employee) => {
              const weeklyHours = getWeeklyHours(employee.id);
              const attendanceRate = getAttendanceRate(employee.id);
              
              return (
                <TableRow key={employee.id}>
                  <TableCell className="font-medium">{employee.name}</TableCell>
                  <TableCell>{employee.role}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(weeklyHours)}>
                      {weeklyHours.toFixed(1)}h
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${Math.min(attendanceRate, 100)}%` }}
                        ></div>
                      </div>
                      <span className="text-sm">{attendanceRate.toFixed(0)}%</span>
                    </div>
                  </TableCell>
                  <TableCell className="text-sm text-gray-600">
                    {employee.workingHours.start} - {employee.workingHours.end}
                  </TableCell>
                  <TableCell>
                    <Badge variant={employee.isActive ? 'default' : 'secondary'}>
                      {employee.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
        
        {employees.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Users className="h-12 w-12 mx-auto mb-2 text-gray-400" />
            <p>No employees found. Add team members to view statistics.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
