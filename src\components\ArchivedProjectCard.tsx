
import React from 'react';
import { Calendar, Users, Clock, ExternalLink } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Project } from '@/stores/projectStore';

interface ArchivedProjectCardProps {
  project: Project;
}

export function ArchivedProjectCard({ project }: ArchivedProjectCardProps) {
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  return (
    <Card className="hover:shadow-lg transition-all duration-200">
      <CardContent className="p-6">
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="font-semibold text-gray-900">{project.title}</h3>
              <p className="text-sm text-gray-500 mt-1">{project.description}</p>
            </div>
            <Badge className="bg-green-100 text-green-800">
              Completed
            </Badge>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Calendar className="h-4 w-4" />
              Completed: {formatDate(project.updatedAt)}
            </div>
            
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Users className="h-4 w-4" />
              {project.teamMembers.length} team members
            </div>
            
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Clock className="h-4 w-4" />
              Duration: {Math.ceil((new Date(project.updatedAt).getTime() - new Date(project.createdAt).getTime()) / (1000 * 60 * 60 * 24))} days
            </div>
          </div>

          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex flex-wrap gap-1">
              {project.tags.slice(0, 2).map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
            <Button variant="outline" size="sm">
              <ExternalLink className="h-4 w-4 mr-2" />
              View
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
