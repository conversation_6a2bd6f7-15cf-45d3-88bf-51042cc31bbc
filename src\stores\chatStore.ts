import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  timestamp: Date;
  isRead: boolean;
  type: 'text' | 'image' | 'file';
}

export interface Conversation {
  id: string;
  participants: string[];
  lastMessage?: Message;
  lastActivity: Date;
  unreadCount: number;
}

interface ChatStore {
  conversations: Conversation[];
  messages: Message[];
  activeConversation: string | null;
  isTyping: { [conversationId: string]: string[] };
  
  // Actions
  sendMessage: (receiverId: string, content: string, type?: Message['type']) => void;
  markAsRead: (conversationId: string, userId: string) => void;
  setActiveConversation: (conversationId: string | null) => void;
  getConversation: (userId1: string, userId2: string) => Conversation | null;
  getMessages: (conversationId: string) => Message[];
  getUnreadCount: (userId: string) => number;
  setTyping: (conversationId: string, userId: string, isTyping: boolean) => void;
}

export const useChatStore = create<ChatStore>()(
  persist(
    (set, get) => ({
      conversations: [
        // Sample conversation
        {
          id: 'conv-1',
          participants: ['current-user', '1'],
          lastActivity: new Date('2024-01-20T10:30:00'),
          unreadCount: 2,
          lastMessage: {
            id: 'msg-2',
            senderId: '1',
            receiverId: 'current-user',
            content: 'Hey, can you review the latest designs?',
            timestamp: new Date('2024-01-20T10:30:00'),
            isRead: false,
            type: 'text',
          }
        }
      ],
      messages: [
        {
          id: 'msg-1',
          senderId: 'current-user',
          receiverId: '1',
          content: 'Hi John! How is the project going?',
          timestamp: new Date('2024-01-20T10:25:00'),
          isRead: true,
          type: 'text',
        },
        {
          id: 'msg-2',
          senderId: '1',
          receiverId: 'current-user',
          content: 'Hey, can you review the latest designs?',
          timestamp: new Date('2024-01-20T10:30:00'),
          isRead: false,
          type: 'text',
        }
      ],
      activeConversation: null,
      isTyping: {},

      sendMessage: (receiverId: string, content: string, type = 'text') => {
        const { conversations, messages } = get();
        const currentUserId = 'current-user'; // In real app, get from auth store
        
        // Create new message
        const newMessage: Message = {
          id: Math.random().toString(36).substr(2, 9),
          senderId: currentUserId,
          receiverId,
          content,
          timestamp: new Date(),
          isRead: false,
          type,
        };

        // Find or create conversation
        let conversation = conversations.find(conv => 
          conv.participants.includes(currentUserId) && conv.participants.includes(receiverId)
        );

        if (!conversation) {
          conversation = {
            id: Math.random().toString(36).substr(2, 9),
            participants: [currentUserId, receiverId],
            lastActivity: new Date(),
            unreadCount: 0,
          };
        }

        // Update conversation
        const updatedConversation = {
          ...conversation,
          lastMessage: newMessage,
          lastActivity: new Date(),
        };

        set({
          messages: [...messages, newMessage],
          conversations: conversation.id 
            ? conversations.map(conv => conv.id === conversation!.id ? updatedConversation : conv)
            : [...conversations, updatedConversation],
        });
      },

      markAsRead: (conversationId: string, userId: string) => {
        const { conversations, messages } = get();
        
        set({
          conversations: conversations.map(conv => 
            conv.id === conversationId 
              ? { ...conv, unreadCount: 0 }
              : conv
          ),
          messages: messages.map(msg => 
            msg.receiverId === userId && !msg.isRead
              ? { ...msg, isRead: true }
              : msg
          ),
        });
      },

      setActiveConversation: (conversationId: string | null) => {
        set({ activeConversation: conversationId });
      },

      getConversation: (userId1: string, userId2: string) => {
        const { conversations } = get();
        return conversations.find(conv => 
          conv.participants.includes(userId1) && conv.participants.includes(userId2)
        ) || null;
      },

      getMessages: (conversationId: string) => {
        const { messages, conversations } = get();
        const conversation = conversations.find(conv => conv.id === conversationId);
        
        if (!conversation) return [];
        
        return messages.filter(msg => 
          (conversation.participants.includes(msg.senderId) && 
           conversation.participants.includes(msg.receiverId))
        ).sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
      },

      getUnreadCount: (userId: string) => {
        const { conversations } = get();
        return conversations
          .filter(conv => conv.participants.includes(userId))
          .reduce((total, conv) => total + conv.unreadCount, 0);
      },

      setTyping: (conversationId: string, userId: string, isTyping: boolean) => {
        const { isTyping: currentTyping } = get();
        const conversationTyping = currentTyping[conversationId] || [];
        
        set({
          isTyping: {
            ...currentTyping,
            [conversationId]: isTyping
              ? [...conversationTyping.filter(id => id !== userId), userId]
              : conversationTyping.filter(id => id !== userId),
          },
        });
      },
    }),
    {
      name: 'chat-storage',
      partialize: (state) => ({
        conversations: state.conversations,
        messages: state.messages,
      }),
    }
  )
);
