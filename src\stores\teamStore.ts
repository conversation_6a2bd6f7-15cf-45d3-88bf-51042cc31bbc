import { create } from 'zustand';
import { useProjectStore as useProjectStoreBase } from './projectStore';

// Re-export the project store for convenience
export const useProjectStore = useProjectStoreBase;

export interface Employee {
  id: string;
  name: string;
  email: string;
  role: string;
  avatar: string;
  skills: string[];
  workingHours: WorkingHours;
  isActive: boolean;
  joinedAt: Date;
}

export interface WorkingHours {
  start: string; // "09:00"
  end: string;   // "17:00"
  timezone: string;
}

export interface TimeEntry {
  id: string;
  employeeId: string;
  projectId: string;
  taskId?: string;
  startTime: Date;
  endTime?: Date;
  duration: number; // in minutes
  description: string;
  date: Date;
}

export interface AttendanceRecord {
  id: string;
  employeeId: string;
  date: Date;
  clockIn?: Date;
  clockOut?: Date;
  status: 'Present' | 'Late' | 'Absent' | 'Early Leave';
  totalHours: number;
}

interface TeamStore {
  employees: Employee[];
  timeEntries: TimeEntry[];
  attendanceRecords: AttendanceRecord[];
  addEmployee: (employee: Omit<Employee, 'id' | 'joinedAt'>) => void;
  updateEmployee: (id: string, updates: Partial<Employee>) => void;
  removeEmployee: (id: string) => void;
  addTimeEntry: (entry: Omit<TimeEntry, 'id'>) => void;
  updateTimeEntry: (id: string, updates: Partial<TimeEntry>) => void;
  addAttendanceRecord: (record: Omit<AttendanceRecord, 'id'>) => void;
  getEmployeesByProject: (projectId: string) => Employee[];
  getTimeEntriesByEmployee: (employeeId: string) => TimeEntry[];
}

export const useTeamStore = create<TeamStore>((set, get) => ({
  employees: [
    {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'Frontend Developer',
      avatar: '/placeholder.svg',
      skills: ['React', 'TypeScript', 'CSS'],
      workingHours: { start: '09:00', end: '17:00', timezone: 'UTC' },
      isActive: true,
      joinedAt: new Date('2023-01-15'),
    },
    {
      id: '2',
      name: 'Sarah Kim',
      email: '<EMAIL>',
      role: 'UI/UX Designer',
      avatar: '/placeholder.svg',
      skills: ['Figma', 'Sketch', 'Prototyping'],
      workingHours: { start: '09:00', end: '17:00', timezone: 'UTC' },
      isActive: true,
      joinedAt: new Date('2023-02-01'),
    },
  ],
  timeEntries: [],
  attendanceRecords: [],
  
  addEmployee: (employeeData) => set((state) => ({
    employees: [...state.employees, {
      ...employeeData,
      id: Math.random().toString(36).substr(2, 9),
      joinedAt: new Date(),
    }]
  })),
  
  updateEmployee: (id, updates) => set((state) => ({
    employees: state.employees.map(emp =>
      emp.id === id ? { ...emp, ...updates } : emp
    )
  })),
  
  removeEmployee: (id) => set((state) => ({
    employees: state.employees.filter(emp => emp.id !== id)
  })),
  
  addTimeEntry: (entryData) => set((state) => ({
    timeEntries: [...state.timeEntries, {
      ...entryData,
      id: Math.random().toString(36).substr(2, 9),
    }]
  })),
  
  updateTimeEntry: (id, updates) => set((state) => ({
    timeEntries: state.timeEntries.map(entry =>
      entry.id === id ? { ...entry, ...updates } : entry
    )
  })),
  
  addAttendanceRecord: (recordData) => set((state) => ({
    attendanceRecords: [...state.attendanceRecords, {
      ...recordData,
      id: Math.random().toString(36).substr(2, 9),
    }]
  })),
  
  getEmployeesByProject: (projectId) => {
    const { employees } = get();
    // This would be filtered based on project assignments
    return employees.filter(emp => emp.isActive);
  },
  
  getTimeEntriesByEmployee: (employeeId) => {
    const { timeEntries } = get();
    return timeEntries.filter(entry => entry.employeeId === employeeId);
  },
}));
