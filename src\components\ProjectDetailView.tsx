import React, { useState } from 'react';
import { ArrowLeft, Plus, Users, Calendar, DollarSign, Clock, Settings, Eye } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Project } from '@/stores/projectStore';
import { useTeamStore } from '@/stores/teamStore';
import { ScreensView } from './ScreensView';
import { KanbanBoard } from './KanbanBoard';
import { AddScreenModal } from './AddScreenModal';

interface ProjectDetailViewProps {
  project: Project;
  onBack: () => void;
}

export function ProjectDetailView({ project, onBack }: ProjectDetailViewProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [isAddScreenModalOpen, setIsAddScreenModalOpen] = useState(false);
  const { employees } = useTeamStore();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'In Progress':
        return 'bg-blue-100 text-blue-800';
      case 'Review':
        return 'bg-yellow-100 text-yellow-800';
      case 'Completed':
        return 'bg-green-100 text-green-800';
      case 'Planning':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High':
        return 'bg-red-100 text-red-800';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'Low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTeamMemberNames = () => {
    return project.teamMembers
      .map(memberId => employees.find(emp => emp.id === memberId)?.name || memberId)
      .join(', ');
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  const totalTasks = project.screens.reduce((total, screen) => total + screen.tasks.length, 0);
  const completedTasks = project.screens.reduce(
    (total, screen) => total + screen.tasks.filter(task => task.status === 'Done').length,
    0
  );
  const progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Projects
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{project.title}</h1>
            <p className="text-gray-600 mt-1">{project.description}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge className={getStatusColor(project.status)}>{project.status}</Badge>
          <Badge className={getPriorityColor(project.priority)}>{project.priority}</Badge>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Project Image */}
      {project.imageUrl && (
        <div className="w-full h-64 rounded-lg overflow-hidden bg-gray-100">
          <img
            src={project.imageUrl}
            alt={project.title}
            className="w-full h-full object-cover"
            onError={(e) => {
              e.currentTarget.style.display = 'none';
            }}
          />
        </div>
      )}

      {/* Project Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-blue-500" />
              <span className="text-sm font-medium">Team Members</span>
            </div>
            <p className="text-2xl font-bold mt-1">{project.teamMembers.length}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-green-500" />
              <span className="text-sm font-medium">Screens</span>
            </div>
            <p className="text-2xl font-bold mt-1">{project.screens.length}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-purple-500" />
              <span className="text-sm font-medium">Progress</span>
            </div>
            <p className="text-2xl font-bold mt-1">{progress}%</p>
          </CardContent>
        </Card>
        
        {project.budget && (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-yellow-500" />
                <span className="text-sm font-medium">Budget</span>
              </div>
              <p className="text-2xl font-bold mt-1">${project.budget.toLocaleString()}</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="screens">Screens</TabsTrigger>
          <TabsTrigger value="kanban">Kanban</TabsTrigger>
          <TabsTrigger value="team">Team</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Project Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {project.clientName && (
                  <div>
                    <span className="text-sm font-medium text-gray-500">Client:</span>
                    <p className="text-sm">{project.clientName}</p>
                  </div>
                )}
                {project.deadline && (
                  <div>
                    <span className="text-sm font-medium text-gray-500">Deadline:</span>
                    <p className="text-sm">{formatDate(project.deadline)}</p>
                  </div>
                )}
                {project.estimatedHours && (
                  <div>
                    <span className="text-sm font-medium text-gray-500">Estimated Hours:</span>
                    <p className="text-sm">{project.estimatedHours}h</p>
                  </div>
                )}
                <div>
                  <span className="text-sm font-medium text-gray-500">Created:</span>
                  <p className="text-sm">{formatDate(project.createdAt)}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-500">Last Updated:</span>
                  <p className="text-sm">{formatDate(project.updatedAt)}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Team Members</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {project.teamMembers.map((memberId) => {
                    const member = employees.find(emp => emp.id === memberId);
                    if (!member) return null;
                    
                    return (
                      <div key={memberId} className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={member.avatar} />
                          <AvatarFallback>
                            {member.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="text-sm font-medium">{member.name}</p>
                          <p className="text-xs text-gray-500">{member.role}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="screens">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold">Project Screens</h3>
            <Button onClick={() => setIsAddScreenModalOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Screen
            </Button>
          </div>
          <ScreensView project={project} />
        </TabsContent>

        <TabsContent value="kanban">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold">Task Board</h3>
          </div>
          <KanbanBoard project={project} />
        </TabsContent>

        <TabsContent value="team">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold">Team Management</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {project.teamMembers.map((memberId) => {
              const member = employees.find(emp => emp.id === memberId);
              if (!member) return null;
              
              return (
                <Card key={memberId}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={member.avatar} />
                        <AvatarFallback>
                          {member.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{member.name}</p>
                        <p className="text-sm text-gray-500">{member.role}</p>
                        <p className="text-xs text-gray-400">{member.email}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>
      </Tabs>

      <AddScreenModal
        isOpen={isAddScreenModalOpen}
        onClose={() => setIsAddScreenModalOpen(false)}
        projectId={project.id}
      />
    </div>
  );
}
