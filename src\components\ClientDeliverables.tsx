
import React from 'react';
import { Download, ExternalLink, Clock, CheckCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

export function ClientDeliverables() {
  const deliverables = [
    {
      id: '1',
      project: 'SaaS Dashboard',
      client: 'TechCorp Inc.',
      type: 'Final Design',
      status: 'Delivered',
      deliveryDate: '2024-01-15',
      downloadUrl: '#',
    },
    {
      id: '2',
      project: 'Mobile App',
      client: 'StartupXYZ',
      type: 'Prototype',
      status: 'Pending',
      deliveryDate: '2024-01-20',
      downloadUrl: '#',
    },
    {
      id: '3',
      project: 'E-commerce Site',
      client: 'ShopMaster',
      type: 'Assets Package',
      status: 'Delivered',
      deliveryDate: '2024-01-10',
      downloadUrl: '#',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Delivered':
        return 'bg-green-100 text-green-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'Overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Client Deliverables</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Project</TableHead>
              <TableHead>Client</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Delivery Date</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {deliverables.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.project}</TableCell>
                <TableCell>{item.client}</TableCell>
                <TableCell>{item.type}</TableCell>
                <TableCell>
                  <Badge className={getStatusColor(item.status)}>
                    {item.status}
                  </Badge>
                </TableCell>
                <TableCell>{item.deliveryDate}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm">
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
