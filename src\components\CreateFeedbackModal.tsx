
import React, { useState } from 'react';
import { X, Star, Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface CreateFeedbackModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CreateFeedbackModal({ isOpen, onClose }: CreateFeedbackModalProps) {
  const [feedback, setFeedback] = useState({
    recipient: '',
    type: 'team',
    rating: 0,
    content: '',
    project: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle feedback submission
    console.log('Feedback submitted:', feedback);
    onClose();
    setFeedback({
      recipient: '',
      type: 'team',
      rating: 0,
      content: '',
      project: '',
    });
  };

  const setRating = (rating: number) => {
    setFeedback(prev => ({ ...prev, rating }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">New Feedback</h3>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="recipient">Recipient</Label>
            <Input
              id="recipient"
              value={feedback.recipient}
              onChange={(e) => setFeedback(prev => ({ ...prev, recipient: e.target.value }))}
              placeholder="Select team member..."
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">Type</Label>
            <select
              id="type"
              value={feedback.type}
              onChange={(e) => setFeedback(prev => ({ ...prev, type: e.target.value }))}
              className="w-full px-3 py-2 border rounded-md"
            >
              <option value="team">Team Feedback</option>
              <option value="performance">Performance Review</option>
              <option value="project">Project Feedback</option>
            </select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="project">Project</Label>
            <Input
              id="project"
              value={feedback.project}
              onChange={(e) => setFeedback(prev => ({ ...prev, project: e.target.value }))}
              placeholder="Related project..."
            />
          </div>

          <div className="space-y-2">
            <Label>Rating</Label>
            <div className="flex items-center gap-1">
              {Array.from({ length: 5 }, (_, i) => (
                <button
                  key={i}
                  type="button"
                  onClick={() => setRating(i + 1)}
                  className="focus:outline-none"
                >
                  <Star
                    className={`h-6 w-6 ${
                      i < feedback.rating
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                </button>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="content">Feedback</Label>
            <textarea
              id="content"
              value={feedback.content}
              onChange={(e) => setFeedback(prev => ({ ...prev, content: e.target.value }))}
              placeholder="Share your feedback..."
              className="w-full px-3 py-2 border rounded-md"
              rows={4}
              required
            />
          </div>

          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" className="bg-pink-500 hover:bg-pink-600">
              <Send className="h-4 w-4 mr-2" />
              Send Feedback
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
