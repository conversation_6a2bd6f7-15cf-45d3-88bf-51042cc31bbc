
import React, { useState } from 'react';
import { Archive as ArchiveIcon, Search, Filter, Calendar, User, Clock, Download, ExternalLink } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useProjectStore } from '@/stores/projectStore';
import { ArchivedProjectCard } from '@/components/ArchivedProjectCard';
import { ClientDeliverables } from '@/components/ClientDeliverables';
import { CompletedTasksOverview } from '@/components/CompletedTasksOverview';

export function Archive() {
  const [activeTab, setActiveTab] = useState('projects');
  const [searchQuery, setSearchQuery] = useState('');
  const [dateFilter, setDateFilter] = useState('all');
  
  const { projects } = useProjectStore();
  const completedProjects = projects.filter(p => p.status === 'Completed');

  const tabs = [
    { id: 'projects', label: 'Completed Projects' },
    { id: 'deliverables', label: 'Client Deliverables' },
    { id: 'tasks', label: 'Completed Tasks' },
  ];

  const stats = [
    {
      title: 'Completed Projects',
      value: completedProjects.length,
      icon: ArchiveIcon,
      color: 'bg-green-100 text-green-600',
    },
    {
      title: 'This Month',
      value: completedProjects.filter(p => 
        new Date(p.updatedAt).getMonth() === new Date().getMonth()
      ).length,
      icon: Calendar,
      color: 'bg-blue-100 text-blue-600',
    },
    {
      title: 'Total Deliverables',
      value: '24',
      icon: Download,
      color: 'bg-purple-100 text-purple-600',
    },
    {
      title: 'Pending Delivery',
      value: '3',
      icon: Clock,
      color: 'bg-orange-100 text-orange-600',
    },
  ];

  const filteredProjects = completedProjects.filter(project => {
    const matchesSearch = project.title.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesSearch;
  });

  const renderContent = () => {
    switch (activeTab) {
      case 'projects':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProjects.map((project) => (
              <ArchivedProjectCard key={project.id} project={project} />
            ))}
            {filteredProjects.length === 0 && (
              <div className="col-span-full text-center py-12">
                <ArchiveIcon className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p className="text-gray-500">No completed projects found.</p>
              </div>
            )}
          </div>
        );
      case 'deliverables':
        return <ClientDeliverables />;
      case 'tasks':
        return <CompletedTasksOverview />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Archive</h1>
          <p className="text-gray-600 mt-1">View completed projects, deliverables, and historical data</p>
        </div>
        <Button className="bg-pink-500 hover:bg-pink-600">
          <Download className="h-4 w-4 mr-2" />
          Export Archive
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${stat.color}`}>
                  <stat.icon className="h-6 w-6" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === tab.id
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search archive..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <select
          value={dateFilter}
          onChange={(e) => setDateFilter(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="all">All Time</option>
          <option value="month">This Month</option>
          <option value="quarter">This Quarter</option>
          <option value="year">This Year</option>
        </select>
      </div>

      {/* Content */}
      {renderContent()}
    </div>
  );
}
