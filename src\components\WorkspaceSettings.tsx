
import React, { useState } from 'react';
import { Globe, Clock, Users, Save } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

export function WorkspaceSettings() {
  const [workspace, setWorkspace] = useState({
    name: 'ProjectHub Inc.',
    timezone: 'America/New_York',
    workingHours: {
      start: '09:00',
      end: '19:00',
    },
    allowOvertime: true,
    trackLocation: false,
    requireCheckIn: true,
  });

  const handleSave = () => {
    console.log('Workspace settings saved:', workspace);
  };

  return (
    <div className="space-y-6">
      {/* General Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            General Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="workspace-name">Workspace Name</Label>
            <Input
              id="workspace-name"
              value={workspace.name}
              onChange={(e) => setWorkspace({...workspace, name: e.target.value})}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="timezone">Default Timezone</Label>
            <select
              id="timezone"
              value={workspace.timezone}
              onChange={(e) => setWorkspace({...workspace, timezone: e.target.value})}
              className="w-full px-3 py-2 border rounded-md"
            >
              <option value="America/New_York">Eastern Time (EST)</option>
              <option value="America/Chicago">Central Time (CST)</option>
              <option value="America/Denver">Mountain Time (MST)</option>
              <option value="America/Los_Angeles">Pacific Time (PST)</option>
              <option value="UTC">UTC</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Working Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Working Hours & Attendance
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start-time">Start Time</Label>
              <Input
                id="start-time"
                type="time"
                value={workspace.workingHours.start}
                onChange={(e) => setWorkspace({
                  ...workspace,
                  workingHours: { ...workspace.workingHours, start: e.target.value }
                })}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="end-time">End Time</Label>
              <Input
                id="end-time"
                type="time"
                value={workspace.workingHours.end}
                onChange={(e) => setWorkspace({
                  ...workspace,
                  workingHours: { ...workspace.workingHours, end: e.target.value }
                })}
              />
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>Allow Overtime</Label>
                <p className="text-sm text-gray-500">Allow employees to work beyond standard hours</p>
              </div>
              <Switch
                checked={workspace.allowOvertime}
                onCheckedChange={(checked) => setWorkspace({...workspace, allowOvertime: checked})}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label>Require Check-in</Label>
                <p className="text-sm text-gray-500">Employees must manually check in/out</p>
              </div>
              <Switch
                checked={workspace.requireCheckIn}
                onCheckedChange={(checked) => setWorkspace({...workspace, requireCheckIn: checked})}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label>Track Location</Label>
                <p className="text-sm text-gray-500">Record employee location during check-in</p>
              </div>
              <Switch
                checked={workspace.trackLocation}
                onCheckedChange={(checked) => setWorkspace({...workspace, trackLocation: checked})}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Team Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Team Management
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Team Size</h4>
              <p className="text-sm text-gray-500">5 active members</p>
            </div>
            <Button variant="outline">Manage Team</Button>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Invite Members</h4>
              <p className="text-sm text-gray-500">Send invitations to new team members</p>
            </div>
            <Button variant="outline">Send Invites</Button>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={handleSave} className="bg-pink-500 hover:bg-pink-600">
          <Save className="h-4 w-4 mr-2" />
          Save Settings
        </Button>
      </div>
    </div>
  );
}
