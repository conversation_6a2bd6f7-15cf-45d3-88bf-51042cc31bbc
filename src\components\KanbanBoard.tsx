import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Project, Task, useProjectStore } from "@/stores/projectStore";
import { useTeamStore } from "@/stores/teamStore";
import {
  closestCorners,
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  Calendar,
  Clock,
  Edit,
  GripVertical,
  MoreHorizontal,
  Plus,
  Trash2,
} from "lucide-react";
import { useState } from "react";
import { AddTaskModal } from "./AddTaskModal";

interface KanbanBoardProps {
  project: Project;
}

const TASK_STATUSES: Task["status"][] = [
  "To Do",
  "In Progress",
  "Review",
  "Done",
];

// Sortable Task Card Component
function SortableTaskCard({ task }: { task: Task }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: task.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const { updateTask, deleteTask } = useProjectStore();
  const { employees } = useTeamStore();

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "High":
        return "bg-red-100 text-red-800 border-red-200";
      case "Medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "Low":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const handleStatusChange = (taskId: string, newStatus: Task["status"]) => {
    updateTask(taskId, { status: newStatus });
  };

  const handleDeleteTask = (taskId: string) => {
    if (confirm("Are you sure you want to delete this task?")) {
      deleteTask(taskId);
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  const getAssignedMember = (memberId: string) => {
    return employees.find((emp) => emp.id === memberId);
  };

  const assignedMember = getAssignedMember(task.assignedTo);
  const isOverdue = task.dueDate && new Date(task.dueDate) < new Date();

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className="mb-3 hover:shadow-md transition-shadow cursor-pointer group"
    >
      <CardContent className="p-3">
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center gap-2 flex-1">
            <div
              {...attributes}
              {...listeners}
              className="cursor-grab active:cursor-grabbing p-1 hover:bg-gray-100 rounded"
            >
              <GripVertical className="h-3 w-3 text-gray-400" />
            </div>
            <h4 className="font-medium text-sm line-clamp-2 flex-1">
              {task.title}
            </h4>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="opacity-0 group-hover:opacity-100 h-6 w-6 p-0"
              >
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Edit className="h-4 w-4 mr-2" />
                Edit Task
              </DropdownMenuItem>
              {TASK_STATUSES.map(
                (status) =>
                  status !== task.status && (
                    <DropdownMenuItem
                      key={status}
                      onClick={() => handleStatusChange(task.id, status)}
                    >
                      Move to {status}
                    </DropdownMenuItem>
                  )
              )}
              <DropdownMenuItem
                onClick={() => handleDeleteTask(task.id)}
                className="text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Task
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {task.description && (
          <p className="text-xs text-gray-600 mb-2 line-clamp-2">
            {task.description}
          </p>
        )}

        <div className="flex items-center gap-1 mb-2">
          <Badge className={getPriorityColor(task.priority)} variant="outline">
            {task.priority}
          </Badge>
          {task.estimatedHours && (
            <Badge variant="outline" className="text-xs">
              <Clock className="h-3 w-3 mr-1" />
              {task.estimatedHours}h
            </Badge>
          )}
        </div>

        {task.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-2">
            {task.tags.slice(0, 2).map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
            {task.tags.length > 2 && (
              <Badge variant="secondary" className="text-xs">
                +{task.tags.length - 2}
              </Badge>
            )}
          </div>
        )}

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {assignedMember && (
              <div className="flex items-center gap-1">
                <Avatar className="h-5 w-5">
                  <AvatarImage src={assignedMember.avatar} />
                  <AvatarFallback className="text-xs">
                    {assignedMember.name
                      .split(" ")
                      .map((n: string) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <span className="text-xs text-gray-600">
                  {assignedMember.name}
                </span>
              </div>
            )}
          </div>

          {task.dueDate && (
            <div
              className={`flex items-center gap-1 text-xs ${
                isOverdue ? "text-red-600" : "text-gray-500"
              }`}
            >
              <Calendar className="h-3 w-3" />
              {formatDate(task.dueDate)}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export function KanbanBoard({ project }: KanbanBoardProps) {
  const [isAddTaskModalOpen, setIsAddTaskModalOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<Task["status"]>("To Do");
  const [selectedScreenId, setSelectedScreenId] = useState<string>("");
  const [activeTask, setActiveTask] = useState<Task | null>(null);
  const { updateTask } = useProjectStore();
  const { employees } = useTeamStore();

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Get all tasks from all screens
  const allTasks = project.screens.flatMap((screen) => screen.tasks);

  const getTasksByStatus = (status: Task["status"]) => {
    return allTasks.filter((task) => task.status === status);
  };

  const getStatusColor = (status: Task["status"]) => {
    switch (status) {
      case "To Do":
        return "bg-gray-50 border-gray-200";
      case "In Progress":
        return "bg-blue-50 border-blue-200";
      case "Review":
        return "bg-yellow-50 border-yellow-200";
      case "Done":
        return "bg-green-50 border-green-200";
      default:
        return "bg-gray-50 border-gray-200";
    }
  };

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const task = allTasks.find((t) => t.id === active.id);
    setActiveTask(task || null);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) {
      setActiveTask(null);
      return;
    }

    const taskId = active.id as string;
    const overId = over.id as string;

    // Check if dropped on a status column
    if (TASK_STATUSES.includes(overId as Task["status"])) {
      const newStatus = overId as Task["status"];
      const currentTask = allTasks.find((t) => t.id === taskId);

      if (currentTask && currentTask.status !== newStatus) {
        updateTask(taskId, { status: newStatus });
      }
    }

    setActiveTask(null);
  };

  const handleAddTask = (status: Task["status"]) => {
    setSelectedStatus(status);
    // Use the first screen if available
    if (project.screens.length > 0) {
      setSelectedScreenId(project.screens[0].id);
      setIsAddTaskModalOpen(true);
    } else {
      alert("Please add a screen first before creating tasks.");
    }
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCorners}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {TASK_STATUSES.map((status) => {
            const tasks = getTasksByStatus(status);

            return (
              <div
                key={status}
                id={status}
                className={`rounded-lg border-2 ${getStatusColor(status)} p-4`}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold text-gray-900">{status}</h3>
                    <Badge variant="secondary">{tasks.length}</Badge>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleAddTask(status)}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>

                <SortableContext
                  items={tasks.map((task) => task.id)}
                  strategy={verticalListSortingStrategy}
                >
                  <div className="space-y-3 min-h-[200px]">
                    {tasks.map((task) => (
                      <SortableTaskCard key={task.id} task={task} />
                    ))}

                    {tasks.length === 0 && (
                      <div className="text-center py-8 text-gray-400">
                        <p className="text-sm">
                          No tasks in {status.toLowerCase()}
                        </p>
                      </div>
                    )}
                  </div>
                </SortableContext>
              </div>
            );
          })}
        </div>

        <DragOverlay>
          {activeTask ? <SortableTaskCard task={activeTask} /> : null}
        </DragOverlay>

        {/* Add Task Modal */}
        <AddTaskModal
          isOpen={isAddTaskModalOpen}
          onClose={() => setIsAddTaskModalOpen(false)}
          screenId={selectedScreenId}
          projectTeamMembers={project.teamMembers}
        />
      </div>
    </DndContext>
  );
}
