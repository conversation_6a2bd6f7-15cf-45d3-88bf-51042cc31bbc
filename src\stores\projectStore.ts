import { create } from "zustand";

export interface Project {
  id: string;
  title: string;
  description: string;
  status: "Planning" | "In Progress" | "Review" | "Completed";
  createdAt: Date;
  updatedAt: Date;
  screens: Screen[];
  teamMembers: string[];
  clientId?: string;
  clientName?: string;
  deadline?: Date;
  tags: string[];
  imageUrl?: string;
  priority: "Low" | "Medium" | "High";
  estimatedHours?: number;
  actualHours?: number;
  budget?: number;
  isArchived: boolean;
}

export interface Screen {
  id: string;
  projectId: string;
  title: string;
  imageUrl: string;
  assignedTo: string[];
  comments: Comment[];
  tasks: Task[];
  order: number;
}

export interface Task {
  id: string;
  title: string;
  description: string;
  status: "To Do" | "In Progress" | "Review" | "Done";
  assignedTo: string;
  priority: "Low" | "Medium" | "High";
  createdAt: Date;
  dueDate?: Date;
  estimatedHours?: number;
  actualHours?: number;
  tags: string[];
  order: number;
  screenId?: string;
}

export interface Comment {
  id: string;
  content: string;
  authorId: string;
  createdAt: Date;
  x?: number;
  y?: number;
}

interface ProjectStore {
  projects: Project[];
  currentProject: Project | null;
  addProject: (
    project: Omit<Project, "id" | "createdAt" | "updatedAt">
  ) => void;
  updateProject: (id: string, updates: Partial<Project>) => void;
  deleteProject: (id: string) => void;
  archiveProject: (id: string) => void;
  setCurrentProject: (project: Project | null) => void;
  addScreen: (
    projectId: string,
    screen: Omit<Screen, "id" | "projectId">
  ) => void;
  updateScreen: (screenId: string, updates: Partial<Screen>) => void;
  deleteScreen: (screenId: string) => void;
  addTask: (screenId: string, task: Omit<Task, "id" | "createdAt">) => void;
  updateTask: (taskId: string, updates: Partial<Task>) => void;
  deleteTask: (taskId: string) => void;
  moveTask: (
    taskId: string,
    newStatus: Task["status"],
    newOrder: number
  ) => void;
  addComment: (
    screenId: string,
    comment: Omit<Comment, "id" | "createdAt">
  ) => void;
  getProjectsByStatus: (status: Project["status"]) => Project[];
  getArchivedProjects: () => Project[];
  getActiveProjects: () => Project[];
}

export const useProjectStore = create<ProjectStore>((set, get) => ({
  projects: [
    {
      id: "sample-1",
      title: "E-commerce Mobile App",
      description:
        "A modern mobile app for online shopping with intuitive UI/UX design",
      status: "In Progress",
      createdAt: new Date("2024-01-15"),
      updatedAt: new Date("2024-01-20"),
      screens: [
        {
          id: "screen-1",
          projectId: "sample-1",
          title: "Login Screen",
          imageUrl:
            "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400",
          assignedTo: ["1", "2"],
          comments: [],
          tasks: [
            {
              id: "task-1",
              title: "Design login form",
              description:
                "Create a clean and modern login form with email and password fields",
              status: "Done",
              assignedTo: "1",
              priority: "High",
              createdAt: new Date("2024-01-16"),
              estimatedHours: 4,
              actualHours: 3.5,
              tags: ["UI", "Design"],
              order: 1,
              screenId: "screen-1",
            },
            {
              id: "task-2",
              title: "Add social login options",
              description: "Implement Google and Facebook login buttons",
              status: "In Progress",
              assignedTo: "2",
              priority: "Medium",
              createdAt: new Date("2024-01-17"),
              estimatedHours: 6,
              actualHours: 2,
              tags: ["Development", "Integration"],
              order: 2,
              screenId: "screen-1",
            },
          ],
          order: 1,
        },
        {
          id: "screen-2",
          projectId: "sample-1",
          title: "Product Catalog",
          imageUrl:
            "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400",
          assignedTo: ["1"],
          comments: [],
          tasks: [
            {
              id: "task-3",
              title: "Design product grid",
              description: "Create responsive product grid layout",
              status: "To Do",
              assignedTo: "1",
              priority: "High",
              createdAt: new Date("2024-01-18"),
              estimatedHours: 8,
              actualHours: 0,
              tags: ["UI", "Responsive"],
              order: 3,
              screenId: "screen-2",
            },
          ],
          order: 2,
        },
      ],
      teamMembers: ["1", "2"],
      clientName: "TechCorp Inc.",
      deadline: new Date("2024-03-15"),
      tags: ["Mobile", "E-commerce", "React Native"],
      imageUrl:
        "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600",
      priority: "High",
      estimatedHours: 120,
      actualHours: 45,
      budget: 15000,
      isArchived: false,
    },
  ],
  currentProject: null,

  addProject: (projectData) =>
    set((state) => ({
      projects: [
        ...state.projects,
        {
          ...projectData,
          id: Math.random().toString(36).substr(2, 9),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
    })),

  updateProject: (id, updates) =>
    set((state) => ({
      projects: state.projects.map((project) =>
        project.id === id
          ? { ...project, ...updates, updatedAt: new Date() }
          : project
      ),
    })),

  deleteProject: (id) =>
    set((state) => ({
      projects: state.projects.filter((project) => project.id !== id),
    })),

  setCurrentProject: (project) => set({ currentProject: project }),

  addScreen: (projectId, screenData) =>
    set((state) => ({
      projects: state.projects.map((project) =>
        project.id === projectId
          ? {
              ...project,
              screens: [
                ...project.screens,
                {
                  ...screenData,
                  id: Math.random().toString(36).substr(2, 9),
                  projectId,
                },
              ],
            }
          : project
      ),
    })),

  updateScreen: (screenId, updates) =>
    set((state) => ({
      projects: state.projects.map((project) => ({
        ...project,
        screens: project.screens.map((screen) =>
          screen.id === screenId ? { ...screen, ...updates } : screen
        ),
      })),
    })),

  addTask: (screenId, taskData) =>
    set((state) => ({
      projects: state.projects.map((project) => ({
        ...project,
        screens: project.screens.map((screen) =>
          screen.id === screenId
            ? {
                ...screen,
                tasks: [
                  ...screen.tasks,
                  {
                    ...taskData,
                    id: Math.random().toString(36).substr(2, 9),
                    createdAt: new Date(),
                  },
                ],
              }
            : screen
        ),
      })),
    })),

  updateTask: (taskId, updates) =>
    set((state) => ({
      projects: state.projects.map((project) => ({
        ...project,
        screens: project.screens.map((screen) => ({
          ...screen,
          tasks: screen.tasks.map((task) =>
            task.id === taskId ? { ...task, ...updates } : task
          ),
        })),
      })),
    })),

  archiveProject: (id) =>
    set((state) => ({
      projects: state.projects.map((project) =>
        project.id === id ? { ...project, isArchived: true } : project
      ),
    })),

  deleteScreen: (screenId) =>
    set((state) => ({
      projects: state.projects.map((project) => ({
        ...project,
        screens: project.screens.filter((screen) => screen.id !== screenId),
      })),
    })),

  deleteTask: (taskId) =>
    set((state) => ({
      projects: state.projects.map((project) => ({
        ...project,
        screens: project.screens.map((screen) => ({
          ...screen,
          tasks: screen.tasks.filter((task) => task.id !== taskId),
        })),
      })),
    })),

  moveTask: (taskId, newStatus, newOrder) =>
    set((state) => ({
      projects: state.projects.map((project) => ({
        ...project,
        screens: project.screens.map((screen) => ({
          ...screen,
          tasks: screen.tasks.map((task) =>
            task.id === taskId
              ? { ...task, status: newStatus, order: newOrder }
              : task
          ),
        })),
      })),
    })),

  addComment: (screenId, commentData) =>
    set((state) => ({
      projects: state.projects.map((project) => ({
        ...project,
        screens: project.screens.map((screen) =>
          screen.id === screenId
            ? {
                ...screen,
                comments: [
                  ...screen.comments,
                  {
                    ...commentData,
                    id: Math.random().toString(36).substr(2, 9),
                    createdAt: new Date(),
                  },
                ],
              }
            : screen
        ),
      })),
    })),

  getProjectsByStatus: (status) => {
    const { projects } = get();
    return projects.filter(
      (project) => project.status === status && !project.isArchived
    );
  },

  getArchivedProjects: () => {
    const { projects } = get();
    return projects.filter((project) => project.isArchived);
  },

  getActiveProjects: () => {
    const { projects } = get();
    return projects.filter((project) => !project.isArchived);
  },
}));
