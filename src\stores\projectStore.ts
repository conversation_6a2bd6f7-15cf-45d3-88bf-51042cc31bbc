
import { create } from 'zustand';

export interface Project {
  id: string;
  title: string;
  description: string;
  status: 'Planning' | 'In Progress' | 'Review' | 'Completed';
  createdAt: Date;
  updatedAt: Date;
  screens: Screen[];
  teamMembers: string[];
  clientId?: string;
  deadline?: Date;
  tags: string[];
}

export interface Screen {
  id: string;
  projectId: string;
  title: string;
  imageUrl: string;
  assignedTo: string[];
  comments: Comment[];
  tasks: Task[];
  order: number;
}

export interface Task {
  id: string;
  title: string;
  description: string;
  status: 'To Do' | 'In Progress' | 'Done';
  assignedTo: string;
  priority: 'Low' | 'Medium' | 'High';
  createdAt: Date;
  dueDate?: Date;
}

export interface Comment {
  id: string;
  content: string;
  authorId: string;
  createdAt: Date;
  x?: number;
  y?: number;
}

interface ProjectStore {
  projects: Project[];
  currentProject: Project | null;
  addProject: (project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateProject: (id: string, updates: Partial<Project>) => void;
  deleteProject: (id: string) => void;
  setCurrentProject: (project: Project | null) => void;
  addScreen: (projectId: string, screen: Omit<Screen, 'id' | 'projectId'>) => void;
  updateScreen: (screenId: string, updates: Partial<Screen>) => void;
  addTask: (screenId: string, task: Omit<Task, 'id' | 'createdAt'>) => void;
  updateTask: (taskId: string, updates: Partial<Task>) => void;
}

export const useProjectStore = create<ProjectStore>((set, get) => ({
  projects: [],
  currentProject: null,
  
  addProject: (projectData) => set((state) => ({
    projects: [...state.projects, {
      ...projectData,
      id: Math.random().toString(36).substr(2, 9),
      createdAt: new Date(),
      updatedAt: new Date(),
    }]
  })),
  
  updateProject: (id, updates) => set((state) => ({
    projects: state.projects.map(project =>
      project.id === id ? { ...project, ...updates, updatedAt: new Date() } : project
    )
  })),
  
  deleteProject: (id) => set((state) => ({
    projects: state.projects.filter(project => project.id !== id)
  })),
  
  setCurrentProject: (project) => set({ currentProject: project }),
  
  addScreen: (projectId, screenData) => set((state) => ({
    projects: state.projects.map(project =>
      project.id === projectId
        ? {
            ...project,
            screens: [...project.screens, {
              ...screenData,
              id: Math.random().toString(36).substr(2, 9),
              projectId,
            }]
          }
        : project
    )
  })),
  
  updateScreen: (screenId, updates) => set((state) => ({
    projects: state.projects.map(project => ({
      ...project,
      screens: project.screens.map(screen =>
        screen.id === screenId ? { ...screen, ...updates } : screen
      )
    }))
  })),
  
  addTask: (screenId, taskData) => set((state) => ({
    projects: state.projects.map(project => ({
      ...project,
      screens: project.screens.map(screen =>
        screen.id === screenId
          ? {
              ...screen,
              tasks: [...screen.tasks, {
                ...taskData,
                id: Math.random().toString(36).substr(2, 9),
                createdAt: new Date(),
              }]
            }
          : screen
      )
    }))
  })),
  
  updateTask: (taskId, updates) => set((state) => ({
    projects: state.projects.map(project => ({
      ...project,
      screens: project.screens.map(screen => ({
        ...screen,
        tasks: screen.tasks.map(task =>
          task.id === taskId ? { ...task, ...updates } : task
        )
      }))
    }))
  })),
}));
