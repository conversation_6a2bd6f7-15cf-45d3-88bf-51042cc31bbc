
import React, { useState, useEffect } from 'react';
import { Play, Pause, Square, Clock } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTeamStore } from '@/stores/teamStore';
import { useProjectStore } from '@/stores/projectStore';

export function TimeTracker() {
  const [activeTimer, setActiveTimer] = useState<string | null>(null);
  const [currentTime, setCurrentTime] = useState(0);
  const [selectedProject, setSelectedProject] = useState('');
  const [description, setDescription] = useState('');
  
  const { addTimeEntry, employees } = useTeamStore();
  const { projects } = useProjectStore();

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (activeTimer) {
      interval = setInterval(() => {
        setCurrentTime(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [activeTimer]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const startTimer = () => {
    if (!selectedProject) return;
    setActiveTimer('current');
    setCurrentTime(0);
  };

  const pauseTimer = () => {
    setActiveTimer(null);
  };

  const stopTimer = () => {
    if (currentTime > 0 && selectedProject) {
      addTimeEntry({
        employeeId: '1', // Current user
        projectId: selectedProject,
        startTime: new Date(Date.now() - currentTime * 1000),
        endTime: new Date(),
        duration: Math.floor(currentTime / 60),
        description,
        date: new Date(),
      });
    }
    setActiveTimer(null);
    setCurrentTime(0);
    setDescription('');
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Time Tracker
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Timer Display */}
        <div className="text-center">
          <div className="text-6xl font-mono font-bold text-gray-900 mb-4">
            {formatTime(currentTime)}
          </div>
          <div className="flex items-center justify-center gap-3">
            <Button
              onClick={startTimer}
              disabled={!!activeTimer || !selectedProject}
              className="bg-green-500 hover:bg-green-600"
            >
              <Play className="h-4 w-4 mr-2" />
              Start
            </Button>
            <Button
              onClick={pauseTimer}
              disabled={!activeTimer}
              variant="outline"
            >
              <Pause className="h-4 w-4 mr-2" />
              Pause
            </Button>
            <Button
              onClick={stopTimer}
              disabled={currentTime === 0}
              variant="outline"
            >
              <Square className="h-4 w-4 mr-2" />
              Stop
            </Button>
          </div>
        </div>

        {/* Project Selection */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Project
            </label>
            <select
              value={selectedProject}
              onChange={(e) => setSelectedProject(e.target.value)}
              className="w-full px-3 py-2 border rounded-md"
            >
              <option value="">Choose a project...</option>
              {projects.map((project) => (
                <option key={project.id} value={project.id}>
                  {project.title}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Task Description
            </label>
            <input
              type="text"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="What are you working on?"
              className="w-full px-3 py-2 border rounded-md"
            />
          </div>
        </div>

        {/* Current Status */}
        {activeTimer && (
          <div className="p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-green-800">Currently tracking:</p>
                <p className="text-sm text-green-600">
                  {projects.find(p => p.id === selectedProject)?.title || 'Unknown Project'}
                </p>
              </div>
              <Badge className="bg-green-500">
                <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
                Recording
              </Badge>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
