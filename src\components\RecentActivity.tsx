
import React from 'react';
import { MessageS<PERSON>re, Eye, Share2, Heart, Clock } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

const activities = [
  {
    id: 1,
    user: '<PERSON>',
    action: 'commented on',
    target: 'Mobile Banking App',
    time: '5 minutes ago',
    type: 'comment',
    avatar: 'SK'
  },
  {
    id: 2,
    user: '<PERSON>',
    action: 'viewed',
    target: 'E-commerce Dashboard',
    time: '15 minutes ago',
    type: 'view',
    avatar: 'AM'
  },
  {
    id: 3,
    user: '<PERSON>',
    action: 'shared',
    target: 'SaaS Landing Page',
    time: '1 hour ago',
    type: 'share',
    avatar: 'LW'
  },
  {
    id: 4,
    user: '<PERSON>',
    action: 'liked',
    target: 'iOS App Redesign',
    time: '2 hours ago',
    type: 'like',
    avatar: 'JD'
  },
  {
    id: 5,
    user: '<PERSON>',
    action: 'updated',
    target: 'Mobile Banking App',
    time: '3 hours ago',
    type: 'update',
    avatar: 'SK'
  }
];

export function RecentActivity() {
  const getIcon = (type: string) => {
    switch (type) {
      case 'comment':
        return <MessageSquare className="h-4 w-4 text-blue-500" />;
      case 'view':
        return <Eye className="h-4 w-4 text-green-500" />;
      case 'share':
        return <Share2 className="h-4 w-4 text-purple-500" />;
      case 'like':
        return <Heart className="h-4 w-4 text-red-500" />;
      case 'update':
        return <Clock className="h-4 w-4 text-orange-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Recent Activity</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start gap-3 p-4 hover:bg-gray-50 transition-colors">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-gradient-to-br from-pink-500 to-purple-600 text-white text-xs">
                  {activity.avatar}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  {getIcon(activity.type)}
                  <span className="text-sm font-medium text-gray-900">{activity.user}</span>
                </div>
                <p className="text-sm text-gray-600">
                  {activity.action} <span className="font-medium">{activity.target}</span>
                </p>
                <p className="text-xs text-gray-400 mt-1">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
        
        <div className="p-4 border-t">
          <button className="text-sm text-pink-600 hover:text-pink-700 font-medium">
            View all activity
          </button>
        </div>
      </CardContent>
    </Card>
  );
}
