
import React from 'react';
import { Shield, Key, Smartphone, History } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export function SecuritySettings() {
  const loginHistory = [
    { device: 'MacBook Pro', location: 'San Francisco, CA', time: '2 hours ago', current: true },
    { device: 'iPhone 14', location: 'San Francisco, CA', time: '1 day ago', current: false },
    { device: 'Chrome Browser', location: 'New York, NY', time: '3 days ago', current: false },
  ];

  return (
    <div className="space-y-6">
      {/* Password */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Password & Authentication
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Password</h4>
              <p className="text-sm text-gray-500">Last changed 3 months ago</p>
            </div>
            <Button variant="outline">Change Password</Button>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Two-Factor Authentication</h4>
              <p className="text-sm text-gray-500">Add an extra layer of security</p>
            </div>
            <Button variant="outline">
              <Smartphone className="h-4 w-4 mr-2" />
              Setup 2FA
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Login History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Recent Login Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {loginHistory.map((login, index) => (
              <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                    <Smartphone className="h-5 w-5 text-gray-600" />
                  </div>
                  <div>
                    <p className="font-medium">{login.device}</p>
                    <p className="text-sm text-gray-500">{login.location}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">{login.time}</p>
                  {login.current && (
                    <Badge variant="outline" className="text-green-600">Current</Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
