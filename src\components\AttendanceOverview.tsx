
import React from 'react';
import { Clock, MapPin, AlertTriangle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useTeamStore } from '@/stores/teamStore';

export function AttendanceOverview() {
  const { employees, attendanceRecords } = useTeamStore();

  const todayAttendance = attendanceRecords.filter(
    record => record.date.toDateString() === new Date().toDateString()
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Present':
        return 'bg-green-100 text-green-800';
      case 'Late':
        return 'bg-yellow-100 text-yellow-800';
      case 'Absent':
        return 'bg-red-100 text-red-800';
      case 'Early Leave':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const formatTime = (date: Date | undefined) => {
    if (!date) return '--:--';
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Today's Attendance
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {employees.map((employee) => {
            const attendance = todayAttendance.find(a => a.employeeId === employee.id);
            const status = attendance?.status || 'Absent';
            
            return (
              <div key={employee.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback className="bg-gradient-to-br from-pink-500 to-purple-600 text-white">
                      {getInitials(employee.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium text-gray-900">{employee.name}</p>
                    <p className="text-sm text-gray-500">{employee.role}</p>
                  </div>
                </div>
                
                <div className="text-right">
                  <Badge className={getStatusColor(status)}>
                    {status}
                  </Badge>
                  <div className="text-xs text-gray-500 mt-1">
                    In: {formatTime(attendance?.clockIn)} | Out: {formatTime(attendance?.clockOut)}
                  </div>
                </div>
              </div>
            );
          })}
          
          {employees.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <AlertTriangle className="h-12 w-12 mx-auto mb-2 text-gray-400" />
              <p>No employees found. Add team members to track attendance.</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
