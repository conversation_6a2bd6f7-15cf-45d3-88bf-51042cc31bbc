
import React from 'react';
import { TrendingUp, TrendingDown, Star, MessageSquare } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export function FeedbackStats() {
  const stats = [
    {
      title: 'Average Rating',
      value: '4.2',
      change: '+0.3',
      trend: 'up',
      period: 'vs last month',
    },
    {
      title: 'Response Rate',
      value: '94%',
      change: '+2%',
      trend: 'up',
      period: 'vs last month',
    },
    {
      title: 'Positive Feedback',
      value: '78%',
      change: '+5%',
      trend: 'up',
      period: 'vs last month',
    },
    {
      title: 'Resolution Time',
      value: '2.4 days',
      change: '-0.5',
      trend: 'up',
      period: 'vs last month',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      {stats.map((stat) => (
        <Card key={stat.title}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                <div className="flex items-center gap-1 mt-1">
                  {stat.trend === 'up' ? (
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500" />
                  )}
                  <span className={`text-sm ${stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                    {stat.change}
                  </span>
                  <span className="text-sm text-gray-500">{stat.period}</span>
                </div>
              </div>
              <div className="w-12 h-12 bg-gradient-to-br from-pink-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Star className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
