
import React, { useState } from 'react';
import { MessageSquare, TrendingUp, Users, Star, Plus, Filter, Search } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { FeedbackStats } from '@/components/FeedbackStats';
import { FeedbackList } from '@/components/FeedbackList';
import { TeamPerformance } from '@/components/TeamPerformance';
import { CreateFeedbackModal } from '@/components/CreateFeedbackModal';

export function Feedback() {
  const [activeTab, setActiveTab] = useState('overview');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'team-feedback', label: 'Team Feedback' },
    { id: 'performance', label: 'Performance Reviews' },
    { id: 'client-feedback', label: 'Client Feedback' },
  ];

  const stats = [
    {
      title: 'Total Feedback',
      value: '156',
      change: '+12%',
      trend: 'up',
      icon: MessageSquare,
      color: 'bg-blue-100 text-blue-600',
    },
    {
      title: 'Average Rating',
      value: '4.2',
      change: '+0.3',
      trend: 'up',
      icon: Star,
      color: 'bg-yellow-100 text-yellow-600',
    },
    {
      title: 'Team Satisfaction',
      value: '89%',
      change: '+5%',
      trend: 'up',
      icon: Users,
      color: 'bg-green-100 text-green-600',
    },
    {
      title: 'Response Rate',
      value: '94%',
      change: '+2%',
      trend: 'up',
      icon: TrendingUp,
      color: 'bg-purple-100 text-purple-600',
    },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            <FeedbackStats />
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <FeedbackList type="recent" />
              <TeamPerformance />
            </div>
          </div>
        );
      case 'team-feedback':
        return <FeedbackList type="team" />;
      case 'performance':
        return <TeamPerformance detailed />;
      case 'client-feedback':
        return <FeedbackList type="client" />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Feedback & Reviews</h1>
          <p className="text-gray-600 mt-1">Manage team feedback, performance reviews, and client responses</p>
        </div>
        <Button 
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-pink-500 hover:bg-pink-600"
        >
          <Plus className="h-4 w-4 mr-2" />
          New Feedback
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <div className="flex items-center gap-2">
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <Badge variant="outline" className="text-green-600">
                      {stat.change}
                    </Badge>
                  </div>
                </div>
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${stat.color}`}>
                  <stat.icon className="h-6 w-6" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === tab.id
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search feedback..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <select
          value={typeFilter}
          onChange={(e) => setTypeFilter(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="all">All Types</option>
          <option value="positive">Positive</option>
          <option value="constructive">Constructive</option>
          <option value="urgent">Urgent</option>
        </select>
      </div>

      {/* Content */}
      {renderContent()}

      <CreateFeedbackModal 
        isOpen={isCreateModalOpen} 
        onClose={() => setIsCreateModalOpen(false)} 
      />
    </div>
  );
}
