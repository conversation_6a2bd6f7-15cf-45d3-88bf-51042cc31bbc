import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Project, Screen, useProjectStore } from "@/stores/projectStore";
import { useTeamStore } from "@/stores/teamStore";
import {
  Edit,
  MessageSquare,
  MoreHorizontal,
  Plus,
  Trash2,
  Users,
} from "lucide-react";
import { useState } from "react";
import { AddTaskModal } from "./AddTaskModal";

interface ScreensViewProps {
  project: Project;
}

export function ScreensView({ project }: ScreensViewProps) {
  const [selectedScreen, setSelectedScreen] = useState<Screen | null>(null);
  const [isAddTaskModalOpen, setIsAddTaskModalOpen] = useState(false);
  const { deleteScreen } = useProjectStore();
  const { employees } = useTeamStore();

  const handleDeleteScreen = (screenId: string) => {
    if (confirm("Are you sure you want to delete this screen?")) {
      deleteScreen(screenId);
    }
  };

  const getAssignedMemberNames = (assignedTo: string[]) => {
    return assignedTo
      .map(
        (memberId) =>
          employees.find((emp) => emp.id === memberId)?.name || memberId
      )
      .slice(0, 2)
      .join(", ");
  };

  const getTaskStatusCounts = (screen: Screen) => {
    const counts = {
      "To Do": 0,
      "In Progress": 0,
      Review: 0,
      Done: 0,
    };

    screen.tasks.forEach((task) => {
      counts[task.status]++;
    });

    return counts;
  };

  return (
    <div className="space-y-6">
      {project.screens.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">No screens added yet.</p>
          <p className="text-sm text-gray-400">
            Add your first screen to get started with this project.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {project.screens.map((screen) => {
            const taskCounts = getTaskStatusCounts(screen);
            const totalTasks = screen.tasks.length;
            const completedTasks = taskCounts["Done"];
            const progress =
              totalTasks > 0
                ? Math.round((completedTasks / totalTasks) * 100)
                : 0;

            return (
              <Card
                key={screen.id}
                className="group hover:shadow-lg transition-all duration-200"
              >
                <div className="relative">
                  {/* Screen Image */}
                  <div className="h-48 bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-lg overflow-hidden">
                    {screen.imageUrl ? (
                      <img
                        src={screen.imageUrl}
                        alt={screen.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.style.display = "none";
                          e.currentTarget.parentElement!.innerHTML = `
                            <div class="w-full h-full flex items-center justify-center bg-gray-100">
                              <span class="text-gray-400 text-4xl">🖼️</span>
                            </div>
                          `;
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <span className="text-gray-400 text-4xl">🖼️</span>
                      </div>
                    )}
                  </div>

                  {/* Actions Dropdown */}
                  <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="secondary" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Screen
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedScreen(screen);
                            setIsAddTaskModalOpen(true);
                          }}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Task
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDeleteScreen(screen.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete Screen
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                <CardContent className="p-4">
                  {/* Screen Title */}
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {screen.title}
                  </h3>

                  {/* Progress Bar */}
                  <div className="mb-3">
                    <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                      <span>Progress</span>
                      <span>{progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      />
                    </div>
                  </div>

                  {/* Task Status Badges */}
                  <div className="flex flex-wrap gap-1 mb-3">
                    {Object.entries(taskCounts).map(([status, count]) => {
                      if (count === 0) return null;

                      const colors = {
                        "To Do": "bg-gray-100 text-gray-800",
                        "In Progress": "bg-blue-100 text-blue-800",
                        Review: "bg-yellow-100 text-yellow-800",
                        Done: "bg-green-100 text-green-800",
                      };

                      return (
                        <Badge
                          key={status}
                          className={colors[status as keyof typeof colors]}
                        >
                          {count} {status}
                        </Badge>
                      );
                    })}
                  </div>

                  {/* Assigned Members */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">
                        {screen.assignedTo.length > 0
                          ? getAssignedMemberNames(screen.assignedTo)
                          : "Unassigned"}
                        {screen.assignedTo.length > 2 &&
                          ` +${screen.assignedTo.length - 2}`}
                      </span>
                    </div>

                    <div className="flex items-center gap-1 text-sm text-gray-500">
                      <MessageSquare className="h-4 w-4" />
                      {screen.comments.length}
                    </div>
                  </div>

                  {/* Assigned Avatars */}
                  <div className="flex -space-x-2 mt-3">
                    {screen.assignedTo.slice(0, 3).map((memberId, index) => {
                      const member = employees.find(
                        (emp) => emp.id === memberId
                      );
                      if (!member) return null;

                      return (
                        <Avatar
                          key={index}
                          className="h-6 w-6 border-2 border-white"
                        >
                          <AvatarImage src={member.avatar} />
                          <AvatarFallback className="text-xs bg-gradient-to-br from-pink-500 to-purple-600 text-white">
                            {member.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                      );
                    })}
                    {screen.assignedTo.length > 3 && (
                      <div className="h-6 w-6 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center">
                        <span className="text-xs text-gray-600">
                          +{screen.assignedTo.length - 3}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 mt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => {
                        setSelectedScreen(screen);
                        setIsAddTaskModalOpen(true);
                      }}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Task
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Add Task Modal */}
      {selectedScreen && (
        <AddTaskModal
          isOpen={isAddTaskModalOpen}
          onClose={() => {
            setIsAddTaskModalOpen(false);
            setSelectedScreen(null);
          }}
          screenId={selectedScreen.id}
          projectTeamMembers={project.teamMembers}
        />
      )}
    </div>
  );
}
