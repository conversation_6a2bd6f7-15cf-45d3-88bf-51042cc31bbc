
import React from 'react';
import { Star, MessageSquare, Calendar, User } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

interface FeedbackListProps {
  type: 'recent' | 'team' | 'client';
}

export function FeedbackList({ type }: FeedbackListProps) {
  const feedbackData = [
    {
      id: '1',
      author: '<PERSON>',
      recipient: '<PERSON>',
      type: 'team',
      rating: 5,
      content: 'Excellent work on the dashboard design. Very clean and user-friendly.',
      date: '2024-01-15',
      project: 'SaaS Dashboard',
    },
    {
      id: '2',
      author: 'Client - TechCorp',
      recipient: 'Project Team',
      type: 'client',
      rating: 4,
      content: 'Great progress on the project. Looking forward to the next iteration.',
      date: '2024-01-14',
      project: 'Mobile App',
    },
    {
      id: '3',
      author: '<PERSON>',
      recipient: '<PERSON>',
      type: 'team',
      rating: 4,
      content: 'Good collaboration on the API integration. Could improve documentation.',
      date: '2024-01-13',
      project: 'E-commerce Site',
    },
  ];

  const filteredFeedback = feedbackData.filter(item => 
    type === 'recent' || item.type === type
  );

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          {type === 'recent' ? 'Recent Feedback' : 
           type === 'team' ? 'Team Feedback' : 'Client Feedback'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {filteredFeedback.map((feedback) => (
            <div key={feedback.id} className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-start gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarFallback className="bg-gradient-to-br from-pink-500 to-purple-600 text-white">
                    {getInitials(feedback.author)}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <p className="font-medium text-gray-900">{feedback.author}</p>
                      <p className="text-sm text-gray-500">to {feedback.recipient}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1">
                        {renderStars(feedback.rating)}
                      </div>
                      <Badge variant="outline">{feedback.project}</Badge>
                    </div>
                  </div>
                  
                  <p className="text-gray-700 mb-2">{feedback.content}</p>
                  
                  <div className="flex items-center gap-1 text-sm text-gray-500">
                    <Calendar className="h-4 w-4" />
                    {feedback.date}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
